package preowned_shop.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import preowned_shop.service.LoginService;
import preowned_shop.vo.req.LoginReq;
import preowned_shop.vo.req.RegisterReq;
import preowned_shop.vo.res.LoginRes;

@RestController
public class LoginController
{
    @PostMapping("login")
    public LoginRes login(@RequestBody LoginReq req)
    {
       return new LoginRes(loginService.login(req.toPo()));
    }

    @PostMapping("logout")
    public void logout()
    {
        loginService.logout();
    }

    @PostMapping("register")
    public void register(@RequestBody RegisterReq req)
    {
        loginService.register(req.toPo());
    }

    @Autowired
    private LoginService loginService;
}
