package preowned_shop.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import preowned_shop.dao.UserDao;
import preowned_shop.po.LoginUser;
import preowned_shop.po.User;
import preowned_shop.util.JwtUtil;

@Service
public class LoginService
{
    public LoginUser login(User user)
    {
        // 创建一个UsernamePasswordAuthenticationToken对象
        // 将用户账号和密码传入，以便Security底层进行登录验证
        UsernamePasswordAuthenticationToken authToken =
            new UsernamePasswordAuthenticationToken(
                user.getUsername(),
                user.getPassword()
            );
        // 调用authenticationManager.authenticate方法对用户进行身份验证
        // 返回Authentication对象
        Authentication authenticate = null;
        try
        {
            authenticate = authenticationManager.authenticate(
                authToken
            );
        }
        catch (Exception e)
        {
            throw new RuntimeException("用户名或密码错误");
        }
        // 获取登录用户信息
        LoginUser loginUser = (LoginUser)authenticate.getPrincipal();
        // 获取username
        String username = loginUser.getUsername();
        // 生成JWT的令牌
        String jwt = JwtUtil.createToken(loginUser.getUser());
        // 将token存入loginUser对象
        loginUser.setToken(jwt);
        // 将用户信息保存到redis
        redisTemplate.opsForValue().set("jwt:" + username, loginUser.getUser());
        return loginUser;
    }

    public void logout()
    {
        // 获取SecurityContextHolder保存用户信息
        Authentication auth = SecurityContextHolder.getContext()
            .getAuthentication();
        LoginUser loginUser = (LoginUser) auth.getPrincipal();
        String username = loginUser.getUsername();
        // 删除Redis中的用户信息
        redisTemplate.delete("jwt:" + username);
    }

    @Transactional
    public void register(User user) {
        String username = user.getUsername();
        if (!StringUtils.hasText(username)) {
            throw new RuntimeException("参数username不能为空");
        }
        String pwd = user.getPassword();
        if (!StringUtils.hasText(pwd)) {
            pwd = "123456";
        }
        // 加密
        user.setPassword(
            passwordEncoder.encode(pwd)
        );
        // 插库
        userDao.insert(user);
        System.out.println("注册成功");
    }

    public User getCurrentUser()
    {
        // 获取SecurityContextHolder保存用户信息
        Authentication auth = SecurityContextHolder.getContext()
            .getAuthentication();
        LoginUser loginUser = (LoginUser) auth.getPrincipal();
        String username = loginUser.getUsername();
        User u  = (User)redisTemplate.opsForValue().get("jwt:" + username);
        return u;
    }


    @Autowired
    private AuthenticationManager authenticationManager;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private PasswordEncoder passwordEncoder;
    @Autowired
    private UserDao userDao;
}
