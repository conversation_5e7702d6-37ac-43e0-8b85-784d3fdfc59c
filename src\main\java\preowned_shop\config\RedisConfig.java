package preowned_shop.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

@Configuration
public class RedisConfig
{
//    @Bean
//    public RedisCacheManager cacheManager(RedisConnectionFactory factory)
//    {
//        // 定义“键”的序列化形式（字符串）
//        StringRedisSerializer keySerial = new StringRedisSerializer();
//        // 定义“值”的序列化形式（Json格式）
//        Jackson2JsonRedisSerializer valueSerial = new Jackson2JsonRedisSerializer(Object.class);
//        // 定义json处理规则
//        ObjectMapper om = new ObjectMapper();
//        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
//        om.activateDefaultTyping(
//            LaissezFaireSubTypeValidator.instance,
//            ObjectMapper.DefaultTyping.NON_FINAL,
//            JsonTypeInfo.As.PROPERTY
////            JsonTypeInfo.As.WRAPPER_OBJECT
//        );
//        valueSerial.setObjectMapper(om);
//        RedisCacheConfiguration config =RedisCacheConfiguration.defaultCacheConfig()
//                // 若与配置文件冲突，则这里为准，不设置则默认永不过期 即-1
////                .entryTtl(Duration.ofSeconds(30))
//                .serializeKeysWith(
//                    RedisSerializationContext.SerializationPair.fromSerializer(
//                        keySerial
//                    )
//                )
//                .serializeValuesWith(
//                    RedisSerializationContext.SerializationPair.fromSerializer(
//                        valueSerial
//                    )
//                )
//                .disableCachingNullValues()
//                ;
//        return RedisCacheManager.builder(factory).cacheDefaults(config).build();
//    }

    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory factory)
    {
        // 定义键序列化形式
        StringRedisSerializer keySerial = new StringRedisSerializer();
        // 定义值序列化形式
        Jackson2JsonRedisSerializer valueSerial = new Jackson2JsonRedisSerializer(Object.class);
        // 对值序列化的规则（json映射）
        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        om.activateDefaultTyping(
            LaissezFaireSubTypeValidator.instance,
            ObjectMapper.DefaultTyping.NON_FINAL,
//            JsonTypeInfo.As.PROPERTY,
            JsonTypeInfo.As.WRAPPER_OBJECT
        );
        valueSerial.setObjectMapper(om);

        // 创建自定义的RedisTemplate对象来替代springboot默认生成的RedisTemplate对象
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);
        template.setKeySerializer(keySerial);
        template.setValueSerializer(valueSerial);
        template.setHashValueSerializer(valueSerial);
        template.afterPropertiesSet();
        return template;
    }
}
