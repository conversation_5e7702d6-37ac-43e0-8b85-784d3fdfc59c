2025-07-02 12:25:00.787 INFO  p.PreownedShopApplication - Starting PreownedShopApplication using Java 1.8.0_91 on suubian816 with PID 26476 (C:\Users\<USER>\Desktop\111111\preowned-shop\target\classes started by 文文 in C:\Users\<USER>\Desktop\111111\preowned-shop)
2025-07-02 12:25:00.790 INFO  p.PreownedShopApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-02 12:25:01.543 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-02 12:25:01.545 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-02 12:25:01.585 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
2025-07-02 12:25:02.127 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tom<PERSON> initialized with port(s): 8080 (http)
2025-07-02 12:25:02.135 INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-02 12:25:02.136 INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-02 12:25:02.136 INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-02 12:25:02.360 INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-02 12:25:02.360 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1522 ms
2025-07-02 12:25:03.223 INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7ce85af2, org.springframework.security.web.context.SecurityContextPersistenceFilter@2f521c4, org.springframework.security.web.header.HeaderWriterFilter@65d8dff8, org.springframework.web.filter.CorsFilter@316acbb5, org.springframework.security.web.authentication.logout.LogoutFilter@5a50d9fc, preowned_shop.filter.JwtTokenFilter@3e17a0a1, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@10db6131, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@37b56ac7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@56f730b2, org.springframework.security.web.session.SessionManagementFilter@2d5ef498, org.springframework.security.web.access.ExceptionTranslationFilter@4fa822ad, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@18a25bbd]
2025-07-02 12:25:03.463 INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-02 12:25:03.486 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-02 12:25:03.494 INFO  p.PreownedShopApplication - Started PreownedShopApplication in 3.16 seconds (JVM running for 4.441)
2025-07-02 12:26:53.936 INFO  p.PreownedShopApplication - Starting PreownedShopApplication using Java 1.8.0_91 on suubian816 with PID 5664 (C:\Users\<USER>\Desktop\111111\preowned-shop\target\classes started by 文文 in C:\Users\<USER>\Desktop\111111\preowned-shop)
2025-07-02 12:26:53.938 INFO  p.PreownedShopApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-02 12:26:54.687 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-02 12:26:54.689 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-02 12:26:54.724 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
2025-07-02 12:26:55.222 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-02 12:26:55.231 INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-02 12:26:55.232 INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-02 12:26:55.232 INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-02 12:26:55.391 INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-02 12:26:55.392 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1417 ms
2025-07-02 12:26:56.266 INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6ba060f3, org.springframework.security.web.context.SecurityContextPersistenceFilter@50f097b5, org.springframework.security.web.header.HeaderWriterFilter@7c601d50, org.springframework.web.filter.CorsFilter@7c51782d, org.springframework.security.web.authentication.logout.LogoutFilter@77c233af, preowned_shop.filter.JwtTokenFilter@71ad3d8a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3662bdff, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@65d8dff8, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@77bc2e16, org.springframework.security.web.session.SessionManagementFilter@4860827a, org.springframework.security.web.access.ExceptionTranslationFilter@4288d98e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@34585ac9]
2025-07-02 12:26:56.615 INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-02 12:26:56.649 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-02 12:26:56.660 INFO  p.PreownedShopApplication - Started PreownedShopApplication in 3.09 seconds (JVM running for 4.132)
2025-07-02 12:34:10.837 INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-02 12:34:10.837 INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-02 12:34:10.838 INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-02 12:34:10.996 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-02 12:34:11.431 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-02 12:48:49.552 INFO  p.PreownedShopApplication - Starting PreownedShopApplication using Java 1.8.0_91 on suubian816 with PID 11956 (C:\Users\<USER>\Desktop\111111\preowned-shop\target\classes started by 文文 in C:\Users\<USER>\Desktop\111111\preowned-shop)
2025-07-02 12:48:49.553 INFO  p.PreownedShopApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-02 12:48:50.194 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-02 12:48:50.194 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-02 12:48:50.233 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
2025-07-02 12:48:50.702 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-02 12:48:50.708 INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-07-02 12:48:50.708 INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-02 12:48:50.711 INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-02 12:48:50.861 INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-02 12:48:50.861 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1267 ms
2025-07-02 12:48:51.633 INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@542f6803, org.springframework.security.web.context.SecurityContextPersistenceFilter@380e1909, org.springframework.security.web.header.HeaderWriterFilter@597f0937, org.springframework.web.filter.CorsFilter@5583098b, org.springframework.security.web.authentication.logout.LogoutFilter@7b66322e, preowned_shop.filter.JwtTokenFilter@7139bd31, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@95eb320, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@506a1372, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5807efad, org.springframework.security.web.session.SessionManagementFilter@3ed34ef5, org.springframework.security.web.access.ExceptionTranslationFilter@7add838c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5377414a]
2025-07-02 12:48:51.821 INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-07-02 12:48:51.839 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-07-02 12:48:51.844 INFO  p.PreownedShopApplication - Started PreownedShopApplication in 2.687 seconds (JVM running for 3.696)
2025-07-02 12:48:57.872 INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-02 12:48:57.872 INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-02 12:48:57.873 INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-02 12:50:54.246 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-02 12:50:54.444 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-02 14:08:20.042 ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlocklistedUrls(StrictHttpFirewall.java:535)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:505)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:206)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
2025-07-02 14:08:35.956 ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlocklistedUrls(StrictHttpFirewall.java:535)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:505)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:206)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
2025-07-02 14:11:39.861 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-02 14:11:39.865 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
