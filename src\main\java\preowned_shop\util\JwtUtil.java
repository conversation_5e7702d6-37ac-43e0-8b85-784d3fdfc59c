package preowned_shop.util;

import io.jsonwebtoken.*;
import preowned_shop.po.User;

import java.util.Date;

public class JwtUtil
{
    /**
     * 生成令牌
     * @param user - 载荷内容
     * @return 令牌字符串
     */
    public static String createToken(User user)
    {
        return Jwts.builder()
            // 设置头信息
            .setHeaderParam("typ", "JWT")
            .setHeaderParam("alg", "HS256")
            // 设置载荷信息
            .claim("id", user.getId())
            .claim("username", user.getUsername())
            // 设置令牌有效期为24小时（可以改成配置形式）
            .setExpiration(
                new Date(
                    System.currentTimeMillis() + 1000L * 60L * 60L * 24L
                )
            )
            // 指明签名的算法以及对应密钥
            .signWith(SignatureAlgorithm.HS256, keyByte)
            // 生成令牌字符串
            .compact();

    }

    /**
     * 解析令牌
     * @param token - 传入的令牌字符串
     * @return 解析后的键值对Claims对象
     */
    public static Claims parseToken(String token) {
        JwtParser parser = Jwts.parser().setSigningKey(keyByte);
        Jws<Claims> jws = null;
        try {
            jws = parser.parseClaimsJws(token);
        }
        catch (Exception e) {
            throw new RuntimeException("令牌验证失败");
        }
        return jws.getBody();
    }

    // 设定密钥数据如下，共24字节
    private static final byte[] keyByte = {
        // 0x03    i     S       o    f     t      a    o     &      N    a     n
        0x03, 0x69, 0x53,   0x6F, 0x66, 0x74, 0x61, 0x6F, 0x26, 0x4E, 0x61, 0x6E,
        //   j     i     n       g    &     V      i    c     t      o    r     y
        0x6A, 0x69, 0x6E,   0x67, 0x26, 0x56, 0x69, 0x63, 0x74, 0x6F, 0x72, 0x79
    }; // 24字节的密钥

    public static void main(String[] args)
    {
        // 生成令牌的方法测试
//        User u = new User();
//        u.setId(3L);
//        u.setUsername("cindy");
//        String token = createToken(u);
//        System.out.println(token);
        // 解析令牌的方法测试
        String token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9." +
                "eyJpZCI6MywidXNlcm5hbWUiOiJjaW5keSIsImV4cCI6MTc0OTAwMjg1Nn0." +
                "xKv3XUT1l8TfEqdZM_aTj_J971enQNGxdwFiS3g3Beo";
        Claims claims = parseToken(token);
        Integer id = (Integer)claims.get("id");
        System.out.println("id: " + id);
        String username = (String)claims.get("username");
        System.out.println("username: " + username);
        Date expiration = (Date)claims.getExpiration();
        System.out.println("expiration: " + expiration);
    }
}
