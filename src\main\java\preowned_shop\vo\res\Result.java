package preowned_shop.vo.res;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * 定义了返回给前端应答结构
 * <AUTHOR>
 * @param <T> 返回数据的类型
 */
public class Result<T>
{
    public Result(String code, String msg)
    {
        this.code = code;
        this.msg = msg;
    }
    
    public Result(T data)
    {
        this.data = data;
    }
    
    public Result() {}

    /**
     * 创建成功的结果
     * @param data 数据
     * @param <T> 数据类型
     * @return 结果
     */
    public static <T> Result<T> success(T data) {
        Result<T> result = new Result<>();
        result.setData(data);
        result.setCode("0000");
        result.setMsg("操作成功");
        return result;
    }

    /**
     * 向http应答对象中写入固定结构的应答消息
     * 因为对于过滤器来说，它在ResponseBodyController的外层
     * 因此ResponseBodyController无法拦截，只好直接往应答对象中写
     * @param res
     * @param msg
     * @throws IOException
     */
    public static void writeResponse(
        HttpServletResponse res,
        String msg) throws IOException
    {
        // 设置应答消息的头（contentType）为json格式
        res.setContentType("application/json;charset=utf-8");
        // 设置HTTP状态码为成功（200）
        res.setStatus(HttpServletResponse.SC_OK);
        PrintWriter out = res.getWriter();
        out.write(
            String.format(
                "{\"code\": 9999, \"msg\": \"%s\", \"data\": null}",
                msg
            )
        );
        out.flush();
        out.close();
    }
    
    private String code = "0000";
    private String msg = "操作成功";
    private T data;
    
    public final static Result<?> ok = new Result<>();
    
    public String getCode()
    {
        return code;
    }
    
    public void setCode(String code)
    {
        this.code = code;
    }
    
    public String getMsg()
    {
        return msg;
    }
    
    public void setMsg(String msg)
    {
        this.msg = msg;
    }

    public T getData()
    {
        return data;
    }

    public void setData(T data)
    {
        this.data = data;
    }
}
