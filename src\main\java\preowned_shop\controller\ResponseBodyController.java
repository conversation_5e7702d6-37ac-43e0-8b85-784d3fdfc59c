package preowned_shop.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;
import preowned_shop.vo.res.Result;

@RestControllerAdvice
public class ResponseBodyController implements ResponseBodyAdvice<Object>
{
    @Override
    public boolean supports(
        MethodParameter methodParameter,
        Class<? extends HttpMessageConverter<?>> mc)
    {
        // 打开支持开关
        return true;
    }
    
    @Override
    public Object beforeBodyWrite(
        Object data, MethodParameter methodParameter,
        MediaType mediaType, Class<? extends HttpMessageConverter<?>> mc,
        ServerHttpRequest httpRequest, ServerHttpResponse httpResponse)
    {
        if (data == null)
        {
            // 说明controller的方法没有返回值或者返回了空值,返回没有数据的成功应答
            return Result.ok;
        }
        else if (data instanceof Result)
        {
            // 如果controller方法直接返回Result对象，则原样返回
            return data;
        }
        else if (data instanceof String)
        {
            // 若controller的方法直接返回字符串
            // 则将字符串设置到Result.data中，并返回json格式字符串
            httpResponse.getHeaders().set("Content-Type", "application/json;charset=utf-8");

            ObjectMapper mapper = new JsonMapper();
            String out = null;
            try
            {
                out = mapper.writeValueAsString(Result.success(data));
            }
            catch (Exception e)
            {
                throw new RuntimeException(e);
            }
            return out;
        }
        else
        {
            // 返回非字符串对象, 将数据放到应答的data字段上
            return Result.success(data);
        }
    }
    
    // 定义全局异常处理
    @ExceptionHandler(value = Exception.class)
    public Object errorHandler(Exception e) throws Exception
    {
        e.printStackTrace();
        Result<?> result = new Result<>("9999", e.getMessage());
        return result;
    }
}

