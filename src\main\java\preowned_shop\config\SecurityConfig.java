package preowned_shop.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import preowned_shop.filter.JwtTokenFilter;
import preowned_shop.service.UserService;
import preowned_shop.vo.res.Result;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@EnableWebSecurity
public class SecurityConfig extends WebSecurityConfigurerAdapter
{
    @Override
    protected void configure(AuthenticationManagerBuilder builder) throws Exception
    {
        builder.userDetailsService(userService);
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception
    {
        http.cors() // 启用CORS跨域请求
            .and()
            // 禁用CSRF，因为不使用session
            .csrf().disable()
            // 前后端分离，使用JWT token认证，不需要session
            // 注意：该配置生效会导致handler中的authentication参数为null
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()
            // 处理请求
            .authorizeRequests()
            // 登录、注册和验证码请求不需要认证（不要携带令牌）
            .antMatchers("/login/**", "/register", "/captchaImage").permitAll()
            // 静态资源不要认证
            .antMatchers(
                HttpMethod.GET,
                "/**/*.html", "/**/*.css", "/**/*.js"
            ).permitAll()
            // 其他请求必须要认证（携带令牌）
            .anyRequest().authenticated()
//            .anyRequest().permitAll()
            .and()
            // 添加JWT过滤器，和自定义的JWT令牌生成、解析实现关联
            .addFilterBefore(jwtTokenFilter, UsernamePasswordAuthenticationFilter.class)
            // 添加请求服务时认证失败处理
            .exceptionHandling().authenticationEntryPoint(
                (HttpServletRequest req, HttpServletResponse res, AuthenticationException e) -> {
                    Result.writeResponse(res, "认证失败");
                }
            )
            .and()
            // 前后端一体项目需要设置的，鉴于本项目是前后分离，因此配置被注释
//            .formLogin()    // 使用表单登录方式，默认/login
//            .loginPage("/login")    // 设置登录页
//            .usernameParameter("username")  // 设置前端上送的用户名参数名
//            .passwordParameter("password") // 设置前端上送的密码参数名
//            .defaultSuccessUrl("/") // 设置登录成功的默认主页面
//            .loginProcessingUrl("/login")   // 表单提交地址
//            .failureUrl("/login?error") // 登录失败后跳转的页面
//            .successHandler(successHandler)
//            .failureHandler(failureHandler)
            .logout().logoutUrl("/defaultLogout")  // 设置登出请求地址
            ;
    }

    @Bean
    PasswordEncoder passwordEncoder()
    {
        return new BCryptPasswordEncoder();
    }

    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception
    {
        return super.authenticationManagerBean();
    }

    public static void main(String[] args)
    {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        String pass = encoder.encode("137haha");
        System.out.println(pass);
    }

    @Autowired
    private UserService userService;
    @Autowired
    private JwtTokenFilter jwtTokenFilter;
}
