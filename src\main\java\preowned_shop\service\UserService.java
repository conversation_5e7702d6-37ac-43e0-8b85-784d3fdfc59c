package preowned_shop.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import preowned_shop.dao.RoleDao;
import preowned_shop.dao.UserDao;
import preowned_shop.po.LoginUser;
import preowned_shop.po.Role;
import preowned_shop.po.User;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class UserService implements UserDetailsService
{
    @Override
    public UserDetails loadUserByUsername(String username)
        throws UsernameNotFoundException
    {
        // 根据登录帐号查询相应的用户信息
        User u = userDao.getByUsername(username);
        if (u == null)
        {
            throw new RuntimeException("无效用户");
        }
        // 根据登录帐号查询相应的角色列表
        List<Role> roleList = roleDao.getByUsername(username);

        // 封装成UserDetails对象
        return new LoginUser(
            u,
            roleList,
            roleList.stream().map(
                role -> new SimpleGrantedAuthority(role.getName())
            ).collect(Collectors.toList())
        );
    }

    @Autowired
    private UserDao userDao;
    @Autowired
    private RoleDao roleDao;
}
