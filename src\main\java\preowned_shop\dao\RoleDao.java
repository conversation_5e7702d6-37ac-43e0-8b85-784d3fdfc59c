package preowned_shop.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import preowned_shop.po.Role;

import java.util.List;

@Mapper
public interface RoleDao
{
    @Select(
        "SELECT r.* FROM t_role r " +
        "INNER JOIN t_user_role ur ON r.id = ur.role_id " +
        "INNER JOIN t_user u ON ur.user_id = u.id " +
        "WHERE u.username = #{username}")
    List<Role> getByUsername(String username);
}
