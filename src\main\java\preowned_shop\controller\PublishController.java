package preowned_shop.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import preowned_shop.po.Publish;
import preowned_shop.service.PublishService;
import preowned_shop.vo.req.PublishReq;
import preowned_shop.vo.res.PublishBriefRes;
import preowned_shop.vo.res.PublishListRes;
import preowned_shop.vo.res.Result;

import java.util.List;

@RestController
@RequestMapping("publish")
public class PublishController
{
    @PostMapping
    public void publish(@RequestBody PublishReq req)
    {
        publishService.publish(req.toPo());
    }
    
    /**
     * 获取所有商品列表
     * @return 商品列表
     */
    @GetMapping("/list")
    public Result<List<PublishListRes>> getPublishList()
    {
        List<Publish> publishList = publishService.findAllPublishes();
        return Result.success(PublishListRes.fromPublishList(publishList));
    }
    
    /**
     * 获取简化的商品列表，只包含标题和内容
     * @return 简化的商品列表
     */
    @GetMapping("/visit/list")
    public Result<List<PublishBriefRes>> getBriefPublishList()
    {
        List<Publish> publishList = publishService.findAllPublishes();
        return Result.success(PublishBriefRes.fromPublishList(publishList));
    }
    
    /**
     * 根据ID获取商品详情
     * @param id 商品ID
     * @return 商品详情
     */
    @GetMapping("/{id}")
    public Result<PublishListRes> getPublishById(@PathVariable Long id)
    {
        Publish publish = publishService.findPublishById(id);
        return Result.success(PublishListRes.fromPublish(publish));
    }

    @Autowired
    private PublishService publishService;
}
