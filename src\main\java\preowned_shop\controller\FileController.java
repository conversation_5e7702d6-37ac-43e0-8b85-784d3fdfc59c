package preowned_shop.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import preowned_shop.service.FileService;

import javax.servlet.http.HttpServletResponse;

//@RestController
@Controller
@RequestMapping("file")
public class FileController
{
    @PostMapping("upload")
    @ResponseBody
    public String[] upload(MultipartFile[] file) throws Exception
    {
        return fileService.upload(file);
    }

    @GetMapping("download/{fileName}")
    @ResponseBody
    public void download(
        HttpServletResponse response,
        @PathVariable String fileName) throws Exception
    {
        fileService.download(response, fileName);
    }
    
    /**
     * 根据附件ID下载商品附件
     * @param response HTTP响应对象
     * @param annexId 附件ID
     * @throws Exception 如果下载出错
     */
    @GetMapping("download/annex/{annexId}")
    @ResponseBody
    public void downloadAnnex(
        HttpServletResponse response,
        @PathVariable Long annexId) throws Exception
    {
        fileService.downloadByAnnexId(response, annexId);
    }

    @GetMapping("download/report")
    public String downloadReport(HttpServletResponse response) throws Exception
    {
        String filename = fileService.downloadReport();
        return "redirect:" + filename;
    }

    @Autowired
    private FileService fileService;
}
