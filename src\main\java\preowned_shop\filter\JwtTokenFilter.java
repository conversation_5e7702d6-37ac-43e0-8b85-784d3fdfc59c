package preowned_shop.filter;

import io.jsonwebtoken.Claims;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;
import preowned_shop.po.LoginUser;
import preowned_shop.po.User;
import preowned_shop.util.JwtUtil;
import preowned_shop.vo.res.Result;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;

@Component
public class JwtTokenFilter extends OncePerRequestFilter
{
    @Override
    protected void doFilterInternal(
        HttpServletRequest req, HttpServletResponse res, FilterChain filterChain)
        throws ServletException, IOException
    {
        // 获取请求头中的token
        String token = req.getHeader("token");
        System.out.println(token);
        // 若token不存在，放行并执行后续过滤器
        if (!StringUtils.hasText(token))
        {
            filterChain.doFilter(req, res);
            return;
        }
        // 解析token
        LoginUser user = null;
        Claims claims = null;
        try
        {
            claims = JwtUtil.parseToken(token);
        }
        catch (Exception e)
        {
            Result.writeResponse(res, e.getMessage());
            return;
        }

        String username = (String)claims.get("username");
        if (!StringUtils.hasText(username))
        {
            Result.writeResponse(res, "token非法");
            return;
        }
        Date expiration = (Date)claims.getExpiration();
        Date now = new Date();
        if (expiration.before(now))
        {
            Result.writeResponse(res, "token已过期");
            return;
        }
        // 从redis中获取用户信息
        User u  = (User)redisTemplate.opsForValue().get("jwt:" + username);
        if (u == null)
        {
            Result.writeResponse(res, "用户未登录");
            return;
        }
        /**
         * 需要有一个Authentication类型数据，传入三个参数，表示已认证
         * principal：表示身份验证的主体，一般是username
         * credentials: 表示身份验证的凭证，通常是密码
         * authorities: 表示用户的权限集合
         */
        UsernamePasswordAuthenticationToken authToken =
            new UsernamePasswordAuthenticationToken(new LoginUser(u, null, null), null, null);
        // 存入SecurityContextHolder，以便后续请求的权限验证和授权操作
        SecurityContextHolder.getContext().setAuthentication(authToken);
        // 继续执行下一个过滤器
        filterChain.doFilter(req, res);
    }

    @Autowired
    private RedisTemplate redisTemplate;
}
