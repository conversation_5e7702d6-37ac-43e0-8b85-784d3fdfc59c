package preowned_shop.vo.res;

import lombok.Data;
import preowned_shop.po.Annex;
import preowned_shop.po.Publish;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

@Data
public class PublishListRes {
    private Long id;
    private Long publisherId;
    private String publisherName;
    private Timestamp publishTime;
    private String title;
    private String content;
    private List<AnnexRes> annexes;
    
    @Data
    public static class AnnexRes {
        private Long id;
        private Long publishId;
        private String annexPath;
        
        public static AnnexRes fromAnnex(Annex annex) {
            if (annex == null) {
                return null;
            }
            
            AnnexRes res = new AnnexRes();
            res.setId(annex.getId());
            res.setPublishId(annex.getPublishId());
            res.setAnnexPath(annex.getAnnexPath());
            
            return res;
        }
    }
    
    /**
     * 将Publish对象转换为PublishListRes对象
     * @param publish Publish对象
     * @return PublishListRes对象
     */
    public static PublishListRes fromPublish(Publish publish) {
        if (publish == null) {
            return null;
        }
        
        PublishListRes res = new PublishListRes();
        res.setId(publish.getId());
        res.setPublisherId(publish.getPublisher());
        if (publish.getPublisherUser() != null) {
            res.setPublisherName(publish.getPublisherUser().getRealName());
        }
        res.setPublishTime(publish.getPublishTime());
        res.setTitle(publish.getTitle());
        res.setContent(publish.getContent());
        
        // 转换附件列表
        if (publish.getAnnexes() != null && !publish.getAnnexes().isEmpty()) {
            List<AnnexRes> annexResList = new ArrayList<>();
            for (Annex annex : publish.getAnnexes()) {
                annexResList.add(AnnexRes.fromAnnex(annex));
            }
            res.setAnnexes(annexResList);
        }
        
        return res;
    }
    
    /**
     * 将Publish列表转换为PublishListRes列表
     * @param publishList Publish列表
     * @return PublishListRes列表
     */
    public static List<PublishListRes> fromPublishList(List<Publish> publishList) {
        if (publishList == null) {
            return new ArrayList<>();
        }
        
        List<PublishListRes> resList = new ArrayList<>(publishList.size());
        for (Publish publish : publishList) {
            resList.add(fromPublish(publish));
        }
        
        return resList;
    }
} 