package preowned_shop.dao;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Result;
import preowned_shop.po.Publish;

import java.util.List;

@Mapper
public interface PublishDao
{
    @Insert(
        "INSERT INTO t_publish (" +
            "publisher, publish_time, title,content" +
        ") VALUES (" +
            "#{publisher}, NOW(), #{title}, #{content}" +
        ")"
    )
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(Publish publish);
    
    @Select("SELECT id, publisher, publish_time, title, content FROM t_publish ORDER BY publish_time DESC")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "publisher", column = "publisher"),
        @Result(property = "publishTime", column = "publish_time"),
        @Result(property = "title", column = "title"),
        @Result(property = "content", column = "content")
    })
    List<Publish> findAll();
    
    @Select("SELECT id, publisher, publish_time, title, content FROM t_publish WHERE id = #{id}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "publisher", column = "publisher"),
        @Result(property = "publishTime", column = "publish_time"),
        @Result(property = "title", column = "title"),
        @Result(property = "content", column = "content")
    })
    Publish findById(Long id);
}
