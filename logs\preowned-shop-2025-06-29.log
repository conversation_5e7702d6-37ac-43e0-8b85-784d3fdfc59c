2025-06-29 18:56:57.497 INFO  p.PreownedShopApplication - Starting PreownedShopApplication using Java 1.8.0_91 on geng with PID 45016 (C:\Users\<USER>\Desktop\preowned-shop\target\classes started by 耿 in C:\Users\<USER>\Desktop\preowned-shop)
2025-06-29 18:56:57.498 INFO  p.PreownedShopApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-29 18:56:57.991 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 18:56:57.993 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-29 18:56:58.019 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-06-29 18:56:58.490 INFO  o.s.b.w.e.tomcat.TomcatWebServer - <PERSON><PERSON> initialized with port(s): 8080 (http)
2025-06-29 18:56:58.490 INFO  o.a.c.core.AprLifecycleListener - An older version [1.2.24] of the Apache Tomcat Native library is installed, while Tomcat recommends a minimum version of [1.2.30]
2025-06-29 18:56:58.490 INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.24] using APR version [1.7.0].
2025-06-29 18:56:58.490 INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [false].
2025-06-29 18:56:58.490 INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-29 18:56:58.497 INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1g  21 Apr 2020]
2025-06-29 18:56:58.503 INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-29 18:56:58.504 INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-29 18:56:58.504 INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-29 18:56:58.641 INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-29 18:56:58.641 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1116 ms
2025-06-29 18:56:59.183 INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6ba060f3, org.springframework.security.web.context.SecurityContextPersistenceFilter@1bb15351, org.springframework.security.web.header.HeaderWriterFilter@4d499d65, org.springframework.web.filter.CorsFilter@7c51782d, org.springframework.security.web.authentication.logout.LogoutFilter@35a0e495, preowned_shop.filter.JwtTokenFilter@68e7c8c3, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@597f0937, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@24d61e4, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@77bc2e16, org.springframework.security.web.session.SessionManagementFilter@7add838c, org.springframework.security.web.access.ExceptionTranslationFilter@78a0ff63, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@34585ac9]
2025-06-29 18:56:59.324 INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-29 18:56:59.336 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-29 18:56:59.342 INFO  p.PreownedShopApplication - Started PreownedShopApplication in 2.129 seconds (JVM running for 2.964)
2025-06-29 19:19:30.742 INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-29 19:19:30.742 INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-29 19:19:30.760 INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 18 ms
2025-06-29 19:19:30.924 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-29 19:19:32.173 ERROR com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Exception during pool initialization.
java.sql.SQLException: Access denied for user 'root'@'localhost' (using password: YES)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:129)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceTransactionManager.doBegin(DataSourceTransactionManager.java:265)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.startTransaction(AbstractPlatformTransactionManager.java:400)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:373)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:595)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:382)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at preowned_shop.service.LoginService$$EnhancerBySpringCGLIB$$9ccedaba.register(<generated>)
	at preowned_shop.controller.LoginController.register(LoginController.java:30)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1071)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:964)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:696)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:779)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at preowned_shop.filter.JwtTokenFilter.doFilterInternal(JwtTokenFilter.java:37)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
2025-06-29 19:19:43.396 INFO  p.PreownedShopApplication - Starting PreownedShopApplication using Java 1.8.0_91 on geng with PID 30500 (C:\Users\<USER>\Desktop\preowned-shop\target\classes started by 耿 in C:\Users\<USER>\Desktop\preowned-shop)
2025-06-29 19:19:43.399 INFO  p.PreownedShopApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-29 19:19:43.804 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 19:19:43.805 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-29 19:19:43.823 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-06-29 19:19:44.123 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-29 19:19:44.123 INFO  o.a.c.core.AprLifecycleListener - An older version [1.2.24] of the Apache Tomcat Native library is installed, while Tomcat recommends a minimum version of [1.2.30]
2025-06-29 19:19:44.123 INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.24] using APR version [1.7.0].
2025-06-29 19:19:44.123 INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [false].
2025-06-29 19:19:44.123 INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-29 19:19:44.130 INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1g  21 Apr 2020]
2025-06-29 19:19:44.136 INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-29 19:19:44.136 INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-29 19:19:44.136 INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-29 19:19:44.223 INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-29 19:19:44.223 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 797 ms
2025-06-29 19:19:44.725 INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5807efad, org.springframework.security.web.context.SecurityContextPersistenceFilter@2f521c4, org.springframework.security.web.header.HeaderWriterFilter@65d8dff8, org.springframework.web.filter.CorsFilter@53a84ff4, org.springframework.security.web.authentication.logout.LogoutFilter@5a50d9fc, preowned_shop.filter.JwtTokenFilter@4d8286c4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@10db6131, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@37b56ac7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7ce85af2, org.springframework.security.web.session.SessionManagementFilter@2d5ef498, org.springframework.security.web.access.ExceptionTranslationFilter@4fa822ad, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@17aa8a11]
2025-06-29 19:19:44.880 INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-29 19:19:44.897 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-29 19:19:44.904 INFO  p.PreownedShopApplication - Started PreownedShopApplication in 1.803 seconds (JVM running for 2.411)
2025-06-29 19:20:29.736 INFO  p.PreownedShopApplication - Starting PreownedShopApplication using Java 1.8.0_91 on geng with PID 20572 (C:\Users\<USER>\Desktop\preowned-shop\target\classes started by 耿 in C:\Users\<USER>\Desktop\preowned-shop)
2025-06-29 19:20:29.738 INFO  p.PreownedShopApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-29 19:20:30.125 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 19:20:30.126 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-29 19:20:30.144 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-29 19:20:30.459 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-29 19:20:30.459 INFO  o.a.c.core.AprLifecycleListener - An older version [1.2.24] of the Apache Tomcat Native library is installed, while Tomcat recommends a minimum version of [1.2.30]
2025-06-29 19:20:30.460 INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.24] using APR version [1.7.0].
2025-06-29 19:20:30.460 INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [false].
2025-06-29 19:20:30.460 INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-29 19:20:30.461 INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1g  21 Apr 2020]
2025-06-29 19:20:30.466 INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-29 19:20:30.466 INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-29 19:20:30.466 INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-29 19:20:30.606 INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-29 19:20:30.607 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 850 ms
2025-06-29 19:20:31.134 INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@542f6803, org.springframework.security.web.context.SecurityContextPersistenceFilter@2d5ef498, org.springframework.security.web.header.HeaderWriterFilter@7ad1caa2, org.springframework.web.filter.CorsFilter@5583098b, org.springframework.security.web.authentication.logout.LogoutFilter@63538bb4, preowned_shop.filter.JwtTokenFilter@3e17a0a1, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2f521c4, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@1b7332a7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5807efad, org.springframework.security.web.session.SessionManagementFilter@553bc36c, org.springframework.security.web.access.ExceptionTranslationFilter@3662bdff, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5377414a]
2025-06-29 19:20:31.307 INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-29 19:20:31.326 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-29 19:20:31.331 INFO  p.PreownedShopApplication - Started PreownedShopApplication in 1.819 seconds (JVM running for 2.35)
2025-06-29 19:20:34.605 INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-29 19:20:34.606 INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-29 19:20:34.606 INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-06-29 19:20:34.672 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-29 19:20:34.956 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-29 19:32:43.881 ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlocklistedUrls(StrictHttpFirewall.java:535)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:505)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:206)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
2025-06-29 19:33:00.705 ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlocklistedUrls(StrictHttpFirewall.java:535)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:505)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:206)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
2025-06-29 19:33:27.019 ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception
org.springframework.security.web.firewall.RequestRejectedException: The request was rejected because the URL contained a potentially malicious String "//"
	at org.springframework.security.web.firewall.StrictHttpFirewall.rejectedBlocklistedUrls(StrictHttpFirewall.java:535)
	at org.springframework.security.web.firewall.StrictHttpFirewall.getFirewalledRequest(StrictHttpFirewall.java:505)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:206)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1789)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:745)
2025-06-29 20:00:32.117 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-29 20:00:32.133 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-29 20:00:45.511 INFO  p.PreownedShopApplication - Starting PreownedShopApplication using Java 1.8.0_91 on geng with PID 22140 (C:\Users\<USER>\Desktop\preowned-shop\target\classes started by 耿 in C:\Users\<USER>\Desktop\preowned-shop)
2025-06-29 20:00:45.514 INFO  p.PreownedShopApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-29 20:00:46.365 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 20:00:46.368 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-29 20:00:46.407 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-06-29 20:00:47.300 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-29 20:00:47.300 INFO  o.a.c.core.AprLifecycleListener - An older version [1.2.24] of the Apache Tomcat Native library is installed, while Tomcat recommends a minimum version of [1.2.30]
2025-06-29 20:00:47.300 INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.24] using APR version [1.7.0].
2025-06-29 20:00:47.301 INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [false].
2025-06-29 20:00:47.301 INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-29 20:00:47.305 INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1g  21 Apr 2020]
2025-06-29 20:00:47.314 INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-29 20:00:47.315 INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-29 20:00:47.315 INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-29 20:00:47.466 INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-29 20:00:47.466 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1914 ms
2025-06-29 20:00:48.332 INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5377414a, org.springframework.security.web.context.SecurityContextPersistenceFilter@444f44c5, org.springframework.security.web.header.HeaderWriterFilter@59ce792e, org.springframework.web.filter.CorsFilter@4e83a98, org.springframework.security.web.authentication.logout.LogoutFilter@b34832b, preowned_shop.filter.JwtTokenFilter@ef1695a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@24d61e4, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@380e1909, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@17aa8a11, org.springframework.security.web.session.SessionManagementFilter@6b6b3572, org.springframework.security.web.access.ExceptionTranslationFilter@5cc9d3d0, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6ba060f3]
2025-06-29 20:00:48.551 INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-29 20:00:48.568 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-29 20:00:48.577 INFO  p.PreownedShopApplication - Started PreownedShopApplication in 3.46 seconds (JVM running for 4.87)
2025-06-29 20:00:53.208 INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-29 20:00:53.208 INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-29 20:00:53.209 INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-29 20:00:54.655 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-29 20:00:54.871 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-29 20:06:06.510 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-29 20:06:06.510 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-29 20:06:12.782 INFO  p.PreownedShopApplication - Starting PreownedShopApplication using Java 1.8.0_91 on geng with PID 38708 (C:\Users\<USER>\Desktop\preowned-shop\target\classes started by 耿 in C:\Users\<USER>\Desktop\preowned-shop)
2025-06-29 20:06:12.783 INFO  p.PreownedShopApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-29 20:06:13.175 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 20:06:13.177 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-29 20:06:13.194 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-06-29 20:06:13.531 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-29 20:06:13.532 INFO  o.a.c.core.AprLifecycleListener - An older version [1.2.24] of the Apache Tomcat Native library is installed, while Tomcat recommends a minimum version of [1.2.30]
2025-06-29 20:06:13.532 INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.24] using APR version [1.7.0].
2025-06-29 20:06:13.532 INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [false].
2025-06-29 20:06:13.532 INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-29 20:06:13.535 INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1g  21 Apr 2020]
2025-06-29 20:06:13.543 INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-29 20:06:13.544 INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-29 20:06:13.544 INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-29 20:06:13.643 INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-29 20:06:13.643 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 838 ms
2025-06-29 20:06:14.198 INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5d5b9ecb, org.springframework.security.web.context.SecurityContextPersistenceFilter@57f847af, org.springframework.security.web.header.HeaderWriterFilter@3662bdff, org.springframework.web.filter.CorsFilter@1ee27d73, org.springframework.security.web.authentication.logout.LogoutFilter@fb2e3fd, preowned_shop.filter.JwtTokenFilter@7139bd31, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@553bc36c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@10db6131, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5e5aafc6, org.springframework.security.web.session.SessionManagementFilter@2149594a, org.springframework.security.web.access.ExceptionTranslationFilter@4860827a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@57c00115]
2025-06-29 20:06:14.321 INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-29 20:06:14.331 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-29 20:06:14.336 INFO  p.PreownedShopApplication - Started PreownedShopApplication in 1.809 seconds (JVM running for 2.34)
2025-06-29 20:06:20.875 INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-29 20:06:20.875 INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-29 20:06:20.877 INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-29 20:06:21.751 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-29 20:06:21.887 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-29 20:09:02.004 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-29 20:09:02.017 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-29 20:09:08.237 INFO  p.PreownedShopApplication - Starting PreownedShopApplication using Java 1.8.0_91 on geng with PID 44896 (C:\Users\<USER>\Desktop\preowned-shop\target\classes started by 耿 in C:\Users\<USER>\Desktop\preowned-shop)
2025-06-29 20:09:08.239 INFO  p.PreownedShopApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-29 20:09:08.622 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 20:09:08.623 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-29 20:09:08.642 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-06-29 20:09:08.982 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-29 20:09:08.983 INFO  o.a.c.core.AprLifecycleListener - An older version [1.2.24] of the Apache Tomcat Native library is installed, while Tomcat recommends a minimum version of [1.2.30]
2025-06-29 20:09:08.983 INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.24] using APR version [1.7.0].
2025-06-29 20:09:08.983 INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [false].
2025-06-29 20:09:08.983 INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-29 20:09:08.985 INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1g  21 Apr 2020]
2025-06-29 20:09:08.989 INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-29 20:09:08.990 INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-29 20:09:08.990 INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-29 20:09:09.075 INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-29 20:09:09.075 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 814 ms
2025-06-29 20:09:09.528 INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@71b639d0, org.springframework.security.web.context.SecurityContextPersistenceFilter@1f1e58ca, org.springframework.security.web.header.HeaderWriterFilter@7add838c, org.springframework.web.filter.CorsFilter@18a25bbd, org.springframework.security.web.authentication.logout.LogoutFilter@642413d4, preowned_shop.filter.JwtTokenFilter@81b5db0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3ed34ef5, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4afbb6c2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5d5b9ecb, org.springframework.security.web.session.SessionManagementFilter@24d61e4, org.springframework.security.web.access.ExceptionTranslationFilter@59ce792e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@48e8c32a]
2025-06-29 20:09:09.670 INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-29 20:09:09.681 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-29 20:09:09.686 INFO  p.PreownedShopApplication - Started PreownedShopApplication in 1.71 seconds (JVM running for 2.359)
2025-06-29 20:09:46.569 INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-29 20:09:46.569 INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-29 20:09:46.569 INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-06-29 20:09:48.356 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-29 20:09:48.719 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-29 20:14:45.239 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-29 20:14:45.267 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-29 20:14:51.397 INFO  p.PreownedShopApplication - Starting PreownedShopApplication using Java 1.8.0_91 on geng with PID 46760 (C:\Users\<USER>\Desktop\preowned-shop\target\classes started by 耿 in C:\Users\<USER>\Desktop\preowned-shop)
2025-06-29 20:14:51.399 INFO  p.PreownedShopApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-29 20:14:51.799 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 20:14:51.800 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-29 20:14:51.818 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-06-29 20:14:52.168 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-29 20:14:52.169 INFO  o.a.c.core.AprLifecycleListener - An older version [1.2.24] of the Apache Tomcat Native library is installed, while Tomcat recommends a minimum version of [1.2.30]
2025-06-29 20:14:52.169 INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.24] using APR version [1.7.0].
2025-06-29 20:14:52.169 INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [false].
2025-06-29 20:14:52.169 INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-29 20:14:52.174 INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1g  21 Apr 2020]
2025-06-29 20:14:52.181 INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-29 20:14:52.182 INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-29 20:14:52.182 INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-29 20:14:52.310 INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-29 20:14:52.310 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 889 ms
2025-06-29 20:14:52.890 INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@56f730b2, org.springframework.security.web.context.SecurityContextPersistenceFilter@6c6017b9, org.springframework.security.web.header.HeaderWriterFilter@24d61e4, org.springframework.web.filter.CorsFilter@47311277, org.springframework.security.web.authentication.logout.LogoutFilter@5114b7c7, preowned_shop.filter.JwtTokenFilter@4d8286c4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@506a1372, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5399f6c5, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7930ffa9, org.springframework.security.web.session.SessionManagementFilter@4afbb6c2, org.springframework.security.web.access.ExceptionTranslationFilter@6b6b3572, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1ee27d73]
2025-06-29 20:14:53.002 INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-29 20:14:53.012 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-29 20:14:53.017 INFO  p.PreownedShopApplication - Started PreownedShopApplication in 1.896 seconds (JVM running for 2.428)
2025-06-29 20:16:15.085 INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-29 20:16:15.085 INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-29 20:16:15.086 INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-29 20:16:15.941 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-29 20:16:16.106 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-29 20:17:32.802 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-29 20:17:32.807 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-29 20:17:35.049 INFO  p.PreownedShopApplication - Starting PreownedShopApplication using Java 1.8.0_91 on geng with PID 45708 (C:\Users\<USER>\Desktop\preowned-shop\target\classes started by 耿 in C:\Users\<USER>\Desktop\preowned-shop)
2025-06-29 20:17:35.050 INFO  p.PreownedShopApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-29 20:17:35.434 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 20:17:35.435 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-29 20:17:35.452 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-29 20:17:35.895 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-29 20:17:35.896 INFO  o.a.c.core.AprLifecycleListener - An older version [1.2.24] of the Apache Tomcat Native library is installed, while Tomcat recommends a minimum version of [1.2.30]
2025-06-29 20:17:35.896 INFO  o.a.c.core.AprLifecycleListener - Loaded Apache Tomcat Native library [1.2.24] using APR version [1.7.0].
2025-06-29 20:17:35.896 INFO  o.a.c.core.AprLifecycleListener - APR capabilities: IPv6 [true], sendfile [true], accept filters [false], random [true], UDS [false].
2025-06-29 20:17:35.896 INFO  o.a.c.core.AprLifecycleListener - APR/OpenSSL configuration: useAprConnector [false], useOpenSSL [true]
2025-06-29 20:17:35.899 INFO  o.a.c.core.AprLifecycleListener - OpenSSL successfully initialized [OpenSSL 1.1.1g  21 Apr 2020]
2025-06-29 20:17:35.907 INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-29 20:17:35.908 INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-29 20:17:35.908 INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-29 20:17:35.994 INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-29 20:17:35.994 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 922 ms
2025-06-29 20:17:36.560 INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@542f6803, org.springframework.security.web.context.SecurityContextPersistenceFilter@553bc36c, org.springframework.security.web.header.HeaderWriterFilter@4fa822ad, org.springframework.web.filter.CorsFilter@5583098b, org.springframework.security.web.authentication.logout.LogoutFilter@3f183caa, preowned_shop.filter.JwtTokenFilter@7139bd31, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2d5ef498, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4730e0f0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5807efad, org.springframework.security.web.session.SessionManagementFilter@57f847af, org.springframework.security.web.access.ExceptionTranslationFilter@50f097b5, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5377414a]
2025-06-29 20:17:36.727 INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-29 20:17:36.737 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-29 20:17:36.743 INFO  p.PreownedShopApplication - Started PreownedShopApplication in 1.925 seconds (JVM running for 2.463)
2025-06-29 20:25:50.734 INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-29 20:25:50.734 INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-29 20:25:50.739 INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-06-29 20:35:56.162 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-29 20:35:56.450 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-29 20:53:45.765 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-29 20:53:45.780 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
