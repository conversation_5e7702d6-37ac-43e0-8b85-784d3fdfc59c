package preowned_shop.vo.req;


import preowned_shop.po.User;

import java.sql.Date;

public class RegisterReq extends Request<User>
{
    public RegisterReq()
    {
        super(new User());
    }

    public void setRealName(String realName)
    {
        po.setRealName(realName);
    }

    public void setUsername(String username)
    {
        po.setUsername(username);
    }

    public void setPassword(String password)
    {
        po.setPassword(password);
    }

    public void setGender(Integer gender)
    {
        po.setGender(gender);
    }

    public void setEntryDate(Date entryDate)
    {
        po.setEntryDate(entryDate);
    }
}
