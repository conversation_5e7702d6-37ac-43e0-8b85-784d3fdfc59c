2025-06-26 14:25:34.903 INFO  p.PreownedShopApplication - Starting PreownedShopApplication using Java 1.8.0_411 on J3-405-T1 with PID 11004 (C:\project\preowned-shop\target\classes started by admin in C:\project\preowned-shop)
2025-06-26 14:25:34.915 INFO  p.PreownedShopApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 14:25:36.016 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 14:25:36.019 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 14:25:36.044 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-06-26 14:25:36.568 INFO  o.s.b.w.e.tomcat.TomcatWebServer - <PERSON><PERSON> initialized with port(s): 8080 (http)
2025-06-26 14:25:36.575 INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-26 14:25:36.576 INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 14:25:36.576 INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-26 14:25:36.733 INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 14:25:36.733 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1745 ms
2025-06-26 14:25:37.598 INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1fd9893c, org.springframework.security.web.context.SecurityContextPersistenceFilter@60d40ff4, org.springframework.security.web.header.HeaderWriterFilter@655a01d8, org.springframework.web.filter.CorsFilter@1b2df3aa, org.springframework.security.web.authentication.logout.LogoutFilter@3e4e4c1, preowned_shop.filter.JwtTokenFilter@5e671e20, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@27755487, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6f76c2cc, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@44be69aa, org.springframework.security.web.session.SessionManagementFilter@6a2c717f, org.springframework.security.web.access.ExceptionTranslationFilter@6e43ddd6, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@59c70ceb]
2025-06-26 14:25:37.840 INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-26 14:25:37.858 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-26 14:25:37.867 INFO  p.PreownedShopApplication - Started PreownedShopApplication in 3.635 seconds (JVM running for 5.049)
2025-06-26 15:00:24.989 INFO  p.PreownedShopApplication - Starting PreownedShopApplication using Java 1.8.0_411 on J3-405-T1 with PID 1544 (C:\project\preowned-shop\target\classes started by admin in C:\project\preowned-shop)
2025-06-26 15:00:25.012 INFO  p.PreownedShopApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 15:00:26.344 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 15:00:26.347 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 15:00:26.375 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-06-26 15:00:26.921 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-26 15:00:26.930 INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-26 15:00:26.931 INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 15:00:26.931 INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-26 15:00:27.079 INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 15:00:27.080 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1997 ms
2025-06-26 15:00:27.900 INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@697173d9, org.springframework.security.web.context.SecurityContextPersistenceFilter@5e1fc42f, org.springframework.security.web.header.HeaderWriterFilter@65ddee5a, org.springframework.web.filter.CorsFilter@ceddaf8, org.springframework.security.web.authentication.logout.LogoutFilter@6b6b3572, preowned_shop.filter.JwtTokenFilter@35764bef, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7ee8130e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7c601d50, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3d90eeb3, org.springframework.security.web.session.SessionManagementFilter@2503ec73, org.springframework.security.web.access.ExceptionTranslationFilter@7e7f3cfd, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@21a02556]
2025-06-26 15:00:28.131 INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-26 15:00:28.146 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-26 15:00:28.155 INFO  p.PreownedShopApplication - Started PreownedShopApplication in 4.002 seconds (JVM running for 5.229)
2025-06-26 15:03:31.289 INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 15:03:31.290 INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-26 15:03:31.291 INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-26 15:03:31.398 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-26 15:03:31.945 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-26 15:13:28.516 INFO  p.PreownedShopApplication - Starting PreownedShopApplication using Java 1.8.0_411 on J3-405-T1 with PID 1500 (C:\project\preowned-shop\target\classes started by admin in C:\project\preowned-shop)
2025-06-26 15:13:28.520 INFO  p.PreownedShopApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-26 15:13:29.461 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 15:13:29.463 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 15:13:29.489 INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-06-26 15:13:29.980 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-26 15:13:29.988 INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-26 15:13:29.988 INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 15:13:29.989 INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-26 15:13:30.125 INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 15:13:30.126 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1546 ms
2025-06-26 15:13:30.904 INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@59a79443, org.springframework.security.web.context.SecurityContextPersistenceFilter@77f68df, org.springframework.security.web.header.HeaderWriterFilter@441b8382, org.springframework.web.filter.CorsFilter@493ac8d3, org.springframework.security.web.authentication.logout.LogoutFilter@79b2852b, preowned_shop.filter.JwtTokenFilter@25cd49a4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7e7f3cfd, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@34b9eb03, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@13dbed9e, org.springframework.security.web.session.SessionManagementFilter@77114efe, org.springframework.security.web.access.ExceptionTranslationFilter@7d7cac8, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@40f35e52]
2025-06-26 15:13:31.141 INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-26 15:13:31.157 INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-26 15:13:31.166 INFO  p.PreownedShopApplication - Started PreownedShopApplication in 3.249 seconds (JVM running for 4.352)
2025-06-26 15:13:37.835 INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 15:13:37.836 INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-26 15:13:37.837 INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-26 15:14:00.585 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-26 15:14:00.862 INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
