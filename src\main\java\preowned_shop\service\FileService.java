package preowned_shop.service;

import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.multipart.MultipartFile;
import preowned_shop.dao.AnnexDao;
import preowned_shop.po.Annex;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.util.UUID;

@Service
public class FileService
{
    private String uploadPath;
    private String downloadPath;
    
    @Autowired
    private AnnexDao annexDao;

    public FileService(
        @Value("${file.upload.path}") String uploadPath,
        @Value("${file.download.path}") String downloadPath)
    {
        this.uploadPath = uploadPath;
        this.downloadPath = downloadPath;

        // 验证文件夹是否存在，否则创建
        File dir = new File(uploadPath);
        if (!dir.exists())
        {
            dir.mkdirs();
        }

        // 验证文件夹是否存在，否则创建
        dir = new File(downloadPath);
        if (!dir.exists())
        {
            dir.mkdirs();
        }
    }

    public String[] upload(MultipartFile[] file) throws Exception
    {
        String[] out = new String[file.length];
        int len = 0;
        int i = 0;
        for (MultipartFile f : file)
        {
            // 获取前端过来的原始文件名
            String orignalName = f.getOriginalFilename();
            // 通过原始文件名获得后缀名
            String postfix = orignalName.substring(orignalName.lastIndexOf("."));

            // 随机生成唯一码作为该文件在后端服务器上存储的文件名称
            out[i] = UUID.randomUUID().toString() + postfix;
            f.transferTo(new File(uploadPath + "\\" + out[i]));
            i++;
        }
        return out;
    }

    public void download(
        HttpServletResponse response,
        @PathVariable String fileName) throws Exception
    {
        // 前端传来的文件名称 有内容？
        if (!StringUtils.hasText(fileName))
        {
            return;
        }
        File f = new File(downloadPath + File.separator + fileName);
        if (!f.exists())
        {
            throw new RuntimeException("文件不存在");
        }
        // 文件名转码
        fileName = URLEncoder.encode(fileName, "UTF-8");
        // 设置文件下载头
        response.addHeader(
            "Content-Disposition",
            String.format("attachment;filename=%s", fileName)
        );
        // 设置消息体类型
        response.setContentType("application/octet-stream");
        // 设置消息体大小
        response.setContentLength((int)f.length());
        // 设置应答消息的字符集
        response.setCharacterEncoding("UTF-8");
        // 创建输出流
        OutputStream out = new BufferedOutputStream(response.getOutputStream());
        // 创建读取缓存
        byte[] buffer = FileUtils.readFileToByteArray(f);
        out.write(buffer);
        out.close();
    }
    
    /**
     * 根据附件ID下载附件
     * @param response HTTP响应对象
     * @param annexId 附件ID
     * @throws Exception 如果下载出错
     */
    public void downloadByAnnexId(HttpServletResponse response, Long annexId) throws Exception {
        // 根据附件ID查询附件信息
        Annex annex = annexDao.findById(annexId);
        if (annex == null) {
            throw new RuntimeException("附件不存在");
        }
        
        // 获取附件路径
        String annexPath = annex.getAnnexPath();
        if (!StringUtils.hasText(annexPath)) {
            throw new RuntimeException("附件路径为空");
        }
        
        // 调用下载方法
        download(response, annexPath);
    }

    public String downloadReport() throws Exception
    {
        String filename = "report-" + LocalDate.now() + ".txt";
        // 模拟生成文件
        BufferedWriter out = new BufferedWriter(new FileWriter(downloadPath + "\\" + filename));
        out.write("\t\t\t简易日报\n");
        out.write("用户日活量为：17271\n");
        out.write("用户月活量为：747416\n");
        out.write("页面浏览量为：44147231\n");
        out.write("继续努力！\n");
        out.close();
        return filename;
    }

}
