package preowned_shop.vo.res;

import lombok.Data;
import preowned_shop.po.Publish;

import java.util.ArrayList;
import java.util.List;

/**
 * 只包含标题和内容
 */
@Data
public class PublishBriefRes {
    private Long id;
    private String title;
    private String content;

    /**
     * 将Publish对象转换为PublishBriefRes对象
     * @param publish Publish对象
     * @return PublishBriefRes对象
     */
    public static PublishBriefRes fromPublish(Publish publish) {
        if (publish == null) {
            return null;
        }

        PublishBriefRes res = new PublishBriefRes();
        res.setId(publish.getId());
        res.setTitle(publish.getTitle());
        res.setContent(publish.getContent());

        return res;
    }

    /**
     * 将Publish列表转换为PublishBriefRes列表
     * @param publishList Publish列表
     * @return PublishBriefRes列表
     */
    public static List<PublishBriefRes> fromPublishList(List<Publish> publishList) {
        if (publishList == null) {
            return new ArrayList<>();
        }

        List<PublishBriefRes> resList = new ArrayList<>(publishList.size());
        for (Publish publish : publishList) {
            resList.add(fromPublish(publish));
        }

        return resList;
    }
}
