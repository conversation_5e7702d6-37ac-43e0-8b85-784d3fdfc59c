package preowned_shop.dao;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Result;
import preowned_shop.po.Annex;

import java.util.List;

@Mapper
public interface AnnexDao
{
    @Insert(
        "INSERT INTO t_annex (publish_id, annex_path) " +
        "VALUES (#{publishId}, #{annexPath})"
    )
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(Annex annex);
    
    @Select("SELECT id, publish_id, annex_path FROM t_annex WHERE publish_id = #{publishId}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "publishId", column = "publish_id"),
        @Result(property = "annexPath", column = "annex_path")
    })
    List<Annex> findByPublishId(Long publishId);
    
    @Select("SELECT id, publish_id, annex_path FROM t_annex WHERE id = #{id}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "publishId", column = "publish_id"),
        @Result(property = "annexPath", column = "annex_path")
    })
    Annex findById(Long id);
}
