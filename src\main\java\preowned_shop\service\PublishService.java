package preowned_shop.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import preowned_shop.dao.AnnexDao;
import preowned_shop.dao.PublishDao;
import preowned_shop.dao.UserDao;
import preowned_shop.po.Annex;
import preowned_shop.po.Publish;
import preowned_shop.po.User;

import java.util.List;

@Service
public class PublishService
{
    @Transactional
    public void publish(Publish publish)
    {
        // 获取当前用户
        User currentUser = loginService.getCurrentUser();
        // 设置publish对象的publisher属性值（当前登录用户的id）
        publish.setPublisher(currentUser.getId());
        // 先插入发布表
        publishDao.insert(publish);
        // 再插入附件表
        for (Annex annex : publish.getAnnexes())
        {
            annex.setPublishId(publish.getId());
            annexDao.insert(annex);
        }
    }

    /**
     * 获取所有商品列表
     * @return 商品列表
     */
    public List<Publish> findAllPublishes() {
        List<Publish> publishList = publishDao.findAll();
        // 为每个商品加载附件信息和发布者信息
        if (publishList != null && !publishList.isEmpty()) {
            for (Publish publish : publishList) {
                // 加载附件信息
                List<Annex> annexes = annexDao.findByPublishId(publish.getId());
                publish.setAnnexes(annexes);

                // 加载发布者信息
                User publisher = userDao.getById(publish.getPublisher());
                publish.setPublisherUser(publisher);
            }
        }
        return publishList;
    }

    /**
     * 根据ID获取商品详情
     * @param id 商品ID
     * @return 商品详情
     */
    public Publish findPublishById(Long id) {
        Publish publish = publishDao.findById(id);
        if (publish != null) {
            // 获取商品的附件列表
            List<Annex> annexes = annexDao.findByPublishId(id);
            publish.setAnnexes(annexes);

            // 获取发布者信息
            User publisher = userDao.getById(publish.getPublisher());
            publish.setPublisherUser(publisher);
        }
        return publish;
    }

    @Autowired
    private LoginService loginService;
    @Autowired
    private PublishDao publishDao;
    @Autowired
    private AnnexDao annexDao;
    @Autowired
    private UserDao userDao;
}
