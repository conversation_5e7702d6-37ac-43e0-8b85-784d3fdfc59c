package preowned_shop.dao;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;
import preowned_shop.po.User;

@Mapper
public interface UserDao
{
    @Select("SELECT * FROM t_user WHERE username = #{username}")
    User getByUsername(String username);

    @Insert(
        "INSERT INTO t_user (real_name, username, password, gender, " +
        "register_time, entry_date, valid) " +
        "VALUES (#{realName}, #{username}, #{password}, #{gender}, " +
        "NOW(), #{entryDate}, 1)"
    )
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(User user);
    
    @Select("SELECT * FROM t_user WHERE id = #{id}")
    User getById(Long id);
}
