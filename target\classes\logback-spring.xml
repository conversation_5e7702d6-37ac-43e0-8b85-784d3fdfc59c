<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!--设置日志文件保存路径及文件名前缀-->
    <property name="LOG_PATH" value="logs" />
    <property name="LOG_FILE_NAME" value="preowned-shop" />
    <!--定义标准输出管道-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    <!--定义文件输出管道，按照每天一个文件存储，最多存30天-->
    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${LOG_FILE_NAME}-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <!--当前时间日期格式：yyyy-MM-dd HH:mm:ss.SSS，打印线程名，日志级别左对齐宽度5，日志属主最长36个字符-->
            <!--msg为传入的日志消息，%n为换行符-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    <!--设置日志级别及输出管道-->
    <root level="info">
        <appender-ref ref="console" />
        <appender-ref ref="file" />
    </root>
</configuration>
