package preowned_shop.po;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.List;

@Data
@NoArgsConstructor
public class LoginUser implements UserDetails
{
    public LoginUser(User user, List<Role> roles, List<SimpleGrantedAuthority> authories)
    {
        this.user = user;
        this.roles = roles;
        this.authories = authories;
    }

    @Override
    @JsonIgnore
    public Collection<? extends GrantedAuthority> getAuthorities()
    {
        return null;
    }

    @Override
    @JsonIgnore
    public String getPassword()
    {
        return user.getPassword();
    }

    @Override
    @JsonIgnore
    public String getUsername()
    {
        return user.getUsername();
    }

    @Override
    @JsonIgnore
    public boolean isAccountNonExpired()
    {
        return true;
    }

    @Override
    @JsonIgnore
    public boolean isAccountNonLocked()
    {
        return true;
    }

    @Override
    @JsonIgnore
    public boolean isCredentialsNonExpired()
    {
        return true;
    }

    @Override
    @JsonIgnore
    public boolean isEnabled()
    {
        return true;
    }

    private User user;
    private List<Role> roles;
    private List<SimpleGrantedAuthority> authories;
    private String token;
}
