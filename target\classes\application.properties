# 应用服务 WEB 访问端口
server.port=8080

#配置数据源
spring.datasource.url=*****************************************
spring.datasource.username=root
spring.datasource.password=root

#下面这些内容是为了让MyBatis映射
#指定Mybatis的Mapper文件
mybatis.mapper-locations=classpath:mappers/*xml
#指定Mybatis的实体目录
mybatis.type-aliases-package=sample.po
#mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis.configuration.map-underscore-to-camel-case=true
#mybatis.configuration.call-setters-on-nulls=true

# 配置文件上传大小限制，默认：1M
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=50MB

# 文件上传下载自定义配置
# 文件上传根目录
file.upload.path=C:\\annex
# 文件下载根目录
file.download.path=C:\\annex
